import os

RERANK_MODELS = {
    "default_rerank": {
        "api_url": os.getenv("RERANK_API_URL", "http://localhost:9000"),
        "api_key": os.getenv("RERANK_API_KEY", "test-key"),
        "default_params": {
            "top_k": 20,
            "min_score": 0.5
        }
    }
}
CURRENT_RERANK_MODEL = os.getenv("RERANK_MODEL", "default_rerank")
RERANK_MODEL_CONFIG = RERANK_MODELS[CURRENT_RERANK_MODEL]