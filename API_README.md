# IDP问答助手API服务

本文档介绍如何使用IDP问答助手API服务，该服务封装了LLM问答和RAG问答功能。

## 功能特点

- 支持LLM直接问答和基于检索增强的RAG问答
- 支持流式和非流式响应
- 完善的错误处理机制
- 详细的日志记录

## 安装依赖

确保已安装所有必要的依赖：

```bash
pip install -r requirements.txt
```

## 启动服务

使用提供的启动脚本启动API服务：

```bash
python run_api.py --host 0.0.0.0 --port 8080 --reload
```

参数说明：
- `--host`: 服务主机地址，默认为0.0.0.0
- `--port`: 服务端口，默认为8080
- `--reload`: 启用热重载（开发模式）
- `--workers`: 工作进程数量，默认为1
- `--log-level`: 日志级别，可选值为debug、info、warning、error、critical，默认为info

## API接口

### 1. 健康检查

```
GET /api/v1/health
```

响应示例：

```json
{
  "status": "healthy"
}
```

### 2. LLM问答

```
POST /api/v1/llm-qa
```

请求参数：

```json
{
  "query": "库存周转率如何计算？",
  "user_id": "user123",
  "model_id": "qwen3_32b",
  "request_id": "req_123",  // 可选
  "history": [  // 可选
    {"role": "user", "content": "什么是供应链管理？"}, 
    {"role": "assistant", "content": "供应链管理是..."}
  ],
  "stream": false  // 是否使用流式响应
}
```

响应示例：

```json
{
  "success": true,
  "message": "操作成功",
  "answer": "库存周转率是衡量企业库存管理效率的重要指标...",
  "model_id": "qwen3_32b",
  "request_id": "req_123"
}
```

### 3. RAG问答

```
POST /api/v1/rag-qa
```

请求参数：

```json
{
  "query": "什么是FPC？",
  "user_id": "user123",
  "model_id": "qwen3_32b",
  "request_id": "req_123",  // 可选
  "history": [  // 可选
    {"role": "user", "content": "PCB是什么？"}, 
    {"role": "assistant", "content": "PCB是印刷电路板..."}
  ],
  "stream": false,  // 是否使用流式响应
  "top_k": 10  // 检索的文档数量
}
```

响应示例：

```json
{
  "success": true,
  "message": "操作成功",
  "answer": "FPC是柔性印刷电路板(Flexible Printed Circuit)的缩写...",
  "model_id": "qwen3_32b",
  "request_id": "req_123",
  "references": [
    {
      "title": "FPC基础知识",
      "content": "FPC是柔性印刷电路板...",
      "score": 0.95
    }
  ]
}
```

### 4. DATAQA问答

```
POST /api/v1/data-qa
```

请求参数：

```json
{
  "query": "请给出2023年销售数据分析。",
  "user_id": "user123",
  "model_id": "qwen3_32b",
  "msg_id": "req_123",  // 可选
  "conversation_id": "conv_123", // 可选
  "history": [  // 可选
    {"role": "user", "content": "2022年销售数据如何？"}, 
    {"role": "assistant", "content": "2022年销售数据为..."}
  ],
  "stream": false,  // 是否使用流式响应
  "top_k": 10  // 检索的文档数量
}
```

响应示例：

```json
{
  "success": true,
  "message": "操作成功",
  "answer": "2023年销售数据分析如下...",
  "model_id": "qwen3_32b",
  "msg_id": "req_123",
  "conversation_id": "conv_123",
  "references": [
    {
      "title": "2023年销售报表",
      "content": "2023年销售总额为...",
      "score": 0.98
    }
  ]
}
```

## 流式响应

当`stream`参数设置为`true`时，API将返回Server-Sent Events (SSE)格式的流式响应。客户端需要相应地处理这种格式。

示例响应：

```
data: {"content":"F", "role":"assistant"}

data: {"content":"PC", "role":"assistant"}

data: {"content":"是", "role":"assistant"}

...
```

## 错误处理

API使用标准HTTP状态码表示请求状态：

- 200: 请求成功
- 400: 请求参数错误
- 500: 服务器内部错误

错误响应示例：

```json
{
  "success": false,
  "message": "请求参数无效",
  "error_code": "INVALID_REQUEST",
  "error_detail": {
    "error": "缺少必要参数: query"
  }
}
```

## 测试API

使用提供的测试脚本测试API服务：

```bash
python tests/test_api.py --test-all
```

参数说明：
- `--api-url`: API地址，默认为http://localhost:8080/api/v1
- `--test-all`: 测试所有接口
- `--test-health`: 测试健康检查接口
- `--test-llm`: 测试LLM问答接口
- `--test-rag`: 测试RAG问答接口
- `--test-error`: 测试错误处理
- `--stream`: 使用流式响应
- `--timeout`: 请求超时时间（秒），默认为600秒（10分钟）

如果遇到超时错误，可以增加超时时间：

```bash
python tests/test_api.py --test-all --timeout 900
```

注意：API服务内部已配置了10分钟（600秒）的超时时间，这应该足够处理大多数请求。如果测试脚本仍然出现超时，可能是由于网络问题或服务器负载过高导致的。

## 日志

API服务使用loguru进行日志记录，日志文件默认保存在`/home/<USER>/log/api.log`目录下。可以通过环境变量`log_dir`修改日志目录。

日志格式示例：

```
2023-08-01 12:34:56.789 | INFO | request_id=req_123 | api.routes:llm_qa:50 | LLM问答请求: 库存周转率如何计算？
```


curl -X POST "http://10.38.3.53:8080/api/v1/llm-qa" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "库存周转率如何计算？",
    "user_id": "test_user",
    "model_id": "qwen3_32b",
    "request_id": "YOUR_UUID",
    "history": [
      {"role": "user", "content": "什么是供应链管理？"},
      {"role": "assistant", "content": "供应链管理是指对供应链中的信息流、物流和资金流进行计划、组织、协调与控制的过程。"}
    ],
    "stream": true
  }'