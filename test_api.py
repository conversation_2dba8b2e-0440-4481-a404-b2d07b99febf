import pytest
import aiohttp
import asyncio
import json
import traceback
from api_server import app
import uvicorn
import multiprocessing
import time

async def test_chat_stream_async():
    print("\n开始测试流式响应...")
    print("-" * 50)
    
    async with aiohttp.ClientSession() as session:
        async with session.post(
            "http://localhost:8080/chat/stream",
            json={"message": "什么是量子计算？"}
        ) as response:
            assert response.status == 200
            assert response.headers['content-type'].startswith('text/event-stream')
            
            print("开始接收响应流...")
            async for line in response.content:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    try:
                        data = json.loads(line.replace('data: ', '').strip())
                        if data["type"] == "content":
                            print(f"{data['content']}", end='', flush=True)
                        elif data["type"] == "reasoning":
                            print(f"{data['content']}", end='', flush=True)
                        elif data["type"] == "error":
                            print(f"\n❌ 错误: {data['content']}", flush=True)
                    except json.JSONDecodeError as e:
                        print(f"\n解析错误: {e}")
                    except Exception as e:
                        print(f"\n未知错误: {e}")

def run_server():
    uvicorn.run(app, host="0.0.0.0", port=8080)

def main():
    # 启动服务器进程
    server = multiprocessing.Process(target=run_server)
    server.start()
    
    # 等待服务器启动
    time.sleep(2)
    
    try:
        # 运行异步测试
        asyncio.run(test_chat_stream_async())
    finally:
        # 清理服务器进程
        server.terminate()
        server.join()

if __name__ == "__main__":
    main()