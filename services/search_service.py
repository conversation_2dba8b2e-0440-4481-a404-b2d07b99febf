import httpx
import logging
from config.search_config import SEARCH_MODEL_CONFIG
from loguru import logger
from config.logging_config import configure_logging
configure_logging()

class SearchService:
    def __init__(self, config=None, request_id: str = None):
        self.config = config or SEARCH_MODEL_CONFIG
        self.api_url = self.config["api_url"]
        self.collection_name = self.config["collection_name"]
        self.default_params = self.config.get("default_params", {})
        self.logger = logger.bind(request_id=request_id)

    async def search(self, user_id, query, top_k, search_mode=None, fusion_method=None, **kwargs):
        params = self.default_params.copy()
        params.update({
            "top_k": top_k or self.default_params.get("top_k", 30),
            "search_mode": search_mode or self.default_params.get("search_mode", "hybrid"),
            "fusion_method": fusion_method or self.default_params.get("fusion_method", "rrf"),
        })
        # self.logger.info(f"top_k: {self.default_params['top_k']}")
        # self.logger.info(f"使用参数: {params}")
        params.update(kwargs)
        payload = {
            "collection_name": self.collection_name,
            "user_id": user_id,
            "query": query,
            "additional_params": params
        }
        self.logger.info(f"检索请求参数: {self.api_url}/query, 参数: {payload}")
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.api_url}/query",
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
            self.logger.info(f"检索响应: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                # self.logger.info(f"API响应: {result}")
                search_mode = params["search_mode"]
                search_results = result.get(search_mode) or result.get("hybrid") or (result if isinstance(result, list) else [])
                formatted_results = []
                # for i, item in enumerate(search_results):
                #     score = item.get("score", item.get("distance", 0.0))
                #     formatted_results.append({
                #         "id": i + 1,
                #         "text": item.get("content", ""),
                #         "distance": score,
                #         "rank": i + 1,
                #         "score_type": f"{search_mode} 分数",
                #         "user_id": item.get("user_id", user_id),
                #         "metadata_json": item.get("metadata_json", {})
                #     })
                # return formatted_results, None
                return search_results, None
            else:
                return None, f"API请求失败: {response.status_code} - {response.text}"
        except Exception as e:
            return None, str(e)