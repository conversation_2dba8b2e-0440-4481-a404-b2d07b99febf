"""
FastAPI应用程序入口
该模块创建并配置FastAPI应用程序
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger
import os

from api.routes import router
# 创建FastAPI应用
app = FastAPI(
    title="ipd问答助手",
    description="ipd知识库问答助手",
    version="0.2"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(router, prefix="/api/v1")

@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "ipd问答助手",
        "version": "0.1",
        "status": "运行中"
    }

# 启动事件
@app.on_event("startup")
async def startup_event():
    logger.info("应用程序启动")

# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    logger.info("应用程序关闭")

# 如果直接运行此文件，则启动应用
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app:app", host="0.0.0.0", port=8080, reload=True)