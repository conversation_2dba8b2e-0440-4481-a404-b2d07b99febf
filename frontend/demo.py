#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示脚本 - 展示如何使用Gradio前端应用
"""

import sys
import os
import time
import subprocess

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🤖 智能问答系统 - Gradio前端应用 V2")
    print("=" * 60)
    print("🆕 V2版本新特性:")
    print("✅ 标签页设计 - 三种模式独立标签页")
    print("✅ 优化界面 - 缩小字体，更紧凑布局")
    print("✅ 流式打印 - 思考和回复内容实时显示")
    print("✅ 分区显示 - 知识库参考、思考过程、正式回复")
    print("✅ 多轮对话 - 支持上下文记忆")
    print("✅ 一键清空 - 清空所有标签页历史")
    print("=" * 60)

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    try:
        import gradio
        print(f"✅ Gradio: {gradio.__version__}")
    except ImportError:
        print("❌ Gradio未安装，请运行: pip install gradio")
        return False
    
    try:
        import httpx
        print(f"✅ HTTPX: {httpx.__version__}")
    except ImportError:
        print("❌ HTTPX未安装，请运行: pip install httpx")
        return False
    
    return True

def start_application():
    """启动应用"""
    print("\n🚀 启动Gradio应用...")
    print("📍 访问地址: http://localhost:7860")
    print("⏹️  按 Ctrl+C 停止应用")
    print("-" * 60)
    
    try:
        # 导入并启动应用
        from gradio_app_v2 import create_gradio_interface
        
        interface = create_gradio_interface()
        interface.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            debug=False,
            show_tips=True,
            show_error=True
        )
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

def show_usage_guide():
    """显示使用指南"""
    print("\n📖 V2版本使用指南:")
    print("1. 在左侧配置模型和参数")
    print("2. 选择标签页:")
    print("   - 🤖 Qwen模型: 纯大语言模型问答")
    print("   - 📚 知识库: 基于知识库的检索增强生成")
    print("   - 📊 合问答: 数据问答系统")
    print("3. 在对应标签页输入问题并提交")
    print("4. 查看流式显示的结果:")
    print("   - 对话历史: 完整对话记录")
    print("   - 知识库参考: 相关文档 (知识库/合问答)")
    print("   - 思考过程: AI推理过程")
    print("   - 回复内容: 最终答案")
    print("5. 使用清空按钮重置所有标签页对话")

def main():
    """主函数"""
    print_banner()
    
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请安装所需依赖后重试")
        return
    
    show_usage_guide()
    
    print("\n" + "=" * 60)
    input("按回车键启动应用...")
    
    start_application()

if __name__ == "__main__":
    main()
