# Gradio前端应用 V2

基于Gradio构建的智能问答系统前端界面，支持LLM问答、RAG问答、DATAQA问答三种模式。

## 🆕 V2版本新特性

### 1. 标签页设计
- **🤖 Qwen模型**：纯大语言模型问答
- **📚 知识库**：基于知识库的检索增强生成
- **📊 合问答**：数据问答系统
- 每个标签页独立的输入和显示区域

### 2. 优化的界面设计
- **缩小字体**：更紧凑的界面布局
- **左右分栏**：左侧配置，右侧标签页
- **流式显示**：思考过程和回复内容实时更新
- **分区显示**：知识库参考、思考过程、回复内容分别显示

### 3. API接口调用
- **LLM问答API** (`/api/v1/llm-qa`)：纯大语言模型问答
- **RAG问答API** (`/api/v1/rag-qa`)：基于知识库的检索增强生成
- **DATAQA问答API** (`/api/v1/data-qa`)：数据问答
- 支持流式输出，实时显示回答过程

### 4. 页面布局
- **左侧参数配置区域**：
  - 模型选择
  - Top-K参数设置（检索数量）
  - 清空历史按钮
- **右侧标签页区域**：
  - 三个独立的标签页
  - 每个标签页包含：输入框、对话历史、思考过程、回复内容
  - RAG和DATAQA标签页额外包含知识库/数据参考区域

### 5. 流式输出与分区显示
- **LLM标签页**：
  - 对话历史（完整对话记录）
  - 思考过程和回复内容并排显示
- **RAG/DATAQA标签页**：
  - 对话历史和知识库参考并排显示
  - 思考过程和回复内容并排显示

### 6. 多轮对话
- 自动记录历史问答
- 每次提问时携带历史对话上下文
- 历史对话区展示所有轮次

### 7. 清空历史会话
- 一键清空所有标签页的历史对话记录
- 下次提问时传给API的历史对话为空列表

## 安装依赖

```bash
cd frontend
pip install -r requirements.txt
```

## 启动应用

### 方法1：直接运行主文件
```bash
python gradio_app.py
```

### 方法2：使用启动脚本
```bash
python run_gradio.py
```

### 方法3：自定义参数启动
```bash
python run_gradio.py --host 127.0.0.1 --port 8080 --share --debug
```

## 参数说明

- `--host`：服务器主机地址，默认为0.0.0.0
- `--port`：服务器端口，默认为7860
- `--share`：是否创建公共链接
- `--debug`：是否启用调试模式

## API响应格式

API接口返回的流式数据格式：

```
data: {"type": "reference", "content": "[...]", "role": "", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
data: {"type": "reasoning", "content": "思考内容", "role": "", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
data: {"type": "content", "content": "回答内容", "role": "assistant", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
```

### 字段说明

- `type`：内容类型
  - `reference`：知识库参考（仅RAG/DATAQA）
  - `reasoning`：思考推理过程
  - `content`：正式回答内容
- `content`：具体内容
- `role`：角色（assistant）
- `finish_reason`：结束原因（""表示未结束，"stop"表示正常结束）

## 使用说明

1. **选择API类型**：在左侧选择LLM问答、RAG问答或DATAQA问答
2. **配置参数**：选择模型和Top-K值（RAG/DATAQA）
3. **输入问题**：在输入框中输入您的问题
4. **提交查询**：点击提交按钮或按回车键
5. **查看结果**：
   - 历史对话区显示完整对话历史
   - 知识库参考区显示相关文档（RAG/DATAQA）
   - 思考过程区显示AI的推理过程
   - 正式回复区显示最终答案
6. **清空历史**：需要重新开始对话时点击清空历史按钮

## 注意事项

- 确保后端API服务已启动（默认地址：http://localhost:8080）
- 首次使用时可能需要等待模型加载
- 流式输出过程中请勿重复提交
- 建议在稳定的网络环境下使用
