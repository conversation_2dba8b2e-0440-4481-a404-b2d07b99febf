#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动Gradio前端应用的脚本
"""

import sys
import os
import argparse

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gradio_app_v2 import create_gradio_interface

def main():
    parser = argparse.ArgumentParser(description="启动Gradio前端应用")
    parser.add_argument(
        "--host", 
        type=str, 
        default="0.0.0.0", 
        help="服务器主机地址，默认为0.0.0.0"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=7862,
        help="服务器端口，默认为7862"
    )
    parser.add_argument(
        "--share", 
        action="store_true", 
        help="是否创建公共链接"
    )
    parser.add_argument(
        "--debug", 
        action="store_true", 
        help="是否启用调试模式"
    )
    
    args = parser.parse_args()
    
    print(f"启动Gradio前端应用...")
    print(f"服务器地址: {args.host}:{args.port}")
    print(f"公共链接: {'是' if args.share else '否'}")
    print(f"调试模式: {'是' if args.debug else '否'}")
    
    # 创建并启动界面
    interface = create_gradio_interface()
    interface.launch(
        server_name=args.host,
        server_port=args.port,
        share=args.share,
        debug=args.debug
    )

if __name__ == "__main__":
    main()
