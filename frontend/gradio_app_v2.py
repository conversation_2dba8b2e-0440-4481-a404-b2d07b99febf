#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Gradio的前端应用 - 简化版本
实现LLM问答、RAG问答、DATAQA问答的Web界面
"""

import gradio as gr
import asyncio
import json
import uuid
import httpx
import time
from typing import Dict, Any, List, Optional, Tuple
import traceback
from datetime import datetime
import sys
import os

# 导入配置
try:
    from config import (
        API_BASE_URL, DEFAULT_MODELS, DEFAULT_TOP_K, DEFAULT_MODEL,
        THEME, TITLE, DESCRIPTION, REQUEST_TIMEOUT,
        GRADIO_HOST, GRADIO_PORT, GRADIO_SHARE, GRADIO_DEBUG
    )
except ImportError:
    # 如果配置文件不存在，使用默认值
    API_BASE_URL = "http://localhost:8080/api/v1"
    DEFAULT_MODELS = ["qwen3_32b", "gpt-4", "claude-3"]
    DEFAULT_TOP_K = 3
    DEFAULT_MODEL = "qwen3_32b"
    THEME = "soft"
    TITLE = "🤖 问题库AI"
    # DESCRIPTION = "支持LLM问答、硬工知识库问答、R平台问答三种模式"
    REQUEST_TIMEOUT = 600.0
    GRADIO_HOST = "0.0.0.0"
    GRADIO_PORT = 7862
    GRADIO_SHARE = False
    GRADIO_DEBUG = True

class ChatApp:
    def __init__(self, api_url: str = API_BASE_URL):
        self.api_url = api_url
        # 为每个API类型维护独立的对话历史
        self.llm_conversation_history = []
        self.rag_conversation_history = []
        self.dataqa_conversation_history = []

    def clear_history(self, api_type: str = "all"):
        """清空历史对话"""
        if api_type == "all":
            self.llm_conversation_history = []
            self.rag_conversation_history = []
            self.dataqa_conversation_history = []
        elif api_type == "llm":
            self.llm_conversation_history = []
        elif api_type == "rag":
            self.rag_conversation_history = []
        elif api_type == "dataqa":
            self.dataqa_conversation_history = []
        return ""
    
    async def call_api_stream(self, endpoint: str, payload: Dict[str, Any]):
        """调用API流式接口"""
        url = f"{self.api_url}/{endpoint}"
        responses = []
        
        try:
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                async with client.stream("POST", url, json=payload) as response:
                    response.raise_for_status()
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]
                            try:
                                chunk = json.loads(data)
                                responses.append(chunk)
                            except json.JSONDecodeError:
                                continue
        except Exception as e:
            error_msg = f"API调用失败: {str(e)}"
            responses.append({
                "type": "content",
                "content": error_msg,
                "role": "assistant",
                "finish_reason": "error"
            })
        
        return responses
    
    def process_stream_responses(self, responses: List[Dict[str, Any]]) -> Tuple[str, str, str]:
        """处理流式响应，分离reference、reasoning、content"""
        reference_content = ""
        reasoning_content = ""
        content_content = ""
        
        for chunk in responses:
            chunk_type = chunk.get("type", "")
            chunk_content = chunk.get("content", "")
            
            if chunk_type == "reference":
                reference_content += chunk_content
            elif chunk_type == "reasoning":
                reasoning_content += chunk_content
            elif chunk_type == "content":
                content_content += chunk_content
        
        return reference_content, reasoning_content, content_content
    
    def format_reference_display(self, reference_content: str) -> str:
        """格式化参考内容显示"""
        if not reference_content:
            return ""
        
        try:
            # 尝试解析JSON格式的参考内容
            references = json.loads(reference_content)
            if isinstance(references, list):
                formatted = "📚 **知识库参考：**\n\n"
                for i, ref in enumerate(references, 1):
                    title = ref.get("title", "未知标题")
                    content = ref.get("content", "")
                    doc_name = ref.get("docName", "")
                    doc_url = ref.get("docUrl", "")
                    
                    formatted += f"**参考 {i}：{title}**\n"
                    if doc_name:
                        formatted += f"📄 文档：{doc_name}\n"
                    if doc_url:
                        formatted += f"🔗 链接：{doc_url}\n"
                    if content:
                        formatted += f"📝 内容：{content}\n"
                    formatted += "\n---\n\n"
                return formatted
        except:
            pass
        
        return f"📚 **知识库参考：**\n\n{reference_content}"
    
    async def chat_llm_stream(self, query: str, model_id: str, user_id: str, history_display: str):
        """LLM问答 - 流式输出版本"""
        if not query.strip():
            yield history_display, "", "", "", ""
            return

        # 记录开始时间
        start_time = time.time()
        reasoning_start_time = None
        content_start_time = None

        # 准备请求参数
        request_id = str(uuid.uuid4())
        conversation_id = str(uuid.uuid4())

        payload = {
            "query": query,
            "user_id": user_id,
            "model_id": model_id,
            "msg_id": request_id,
            "conversation_id": conversation_id,
            "history": self.llm_conversation_history[::-1],
            "stream": True
        }

        # 调用API并实时更新
        reasoning_content = ""
        content_content = ""
        reasoning_time = ""
        content_time = ""

        try:
            url = f"{self.api_url}/llm-qa"
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                async with client.stream("POST", url, json=payload) as response:
                    response.raise_for_status()
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]
                            try:
                                chunk = json.loads(data)
                                chunk_type = chunk.get("type", "")
                                chunk_content = chunk.get("content", "")

                                if chunk_type == "reasoning":
                                    if reasoning_start_time is None:
                                        reasoning_start_time = time.time()
                                    reasoning_content += chunk_content
                                    current_time = time.time() - reasoning_start_time
                                    reasoning_time = f"⏱️ {current_time:.2f}秒"
                                    yield history_display, reasoning_content, content_content, reasoning_time, content_time
                                elif chunk_type == "content":
                                    if content_start_time is None:
                                        content_start_time = time.time()
                                        if reasoning_start_time:
                                            reasoning_time = f"⏱️ {content_start_time - reasoning_start_time:.2f}秒"
                                    content_content += chunk_content
                                    current_time = time.time() - content_start_time
                                    content_time = f"⏱️ {current_time:.2f}秒"
                                    yield history_display, reasoning_content, content_content, reasoning_time, content_time
                            except json.JSONDecodeError:
                                continue
        except Exception as e:
            error_msg = f"API调用失败: {str(e)}"
            content_content = error_msg
            yield history_display, reasoning_content, content_content, reasoning_time, content_time

        # 更新历史对话
        self.llm_conversation_history.append({"query": query, "content": content_content})

        # 更新历史显示
        timestamp = datetime.now().strftime("%H:%M:%S")
        final_time = time.time() - start_time
        new_history = f"{history_display}\n\n**[{timestamp}] 用户：**\n{query}\n\n**[{timestamp}] 助手：**\n{content_content}\n⏱️ 总耗时: {final_time:.2f}秒"

        yield new_history, reasoning_content, content_content, reasoning_time, content_time

    async def chat_rag_stream(self, query: str, model_id: str, user_id: str, top_k: int, history_display: str):
        """RAG问答 - 流式输出版本"""
        if not query.strip():
            yield history_display, "", "", "", "", "", ""
            return

        # 记录开始时间
        start_time = time.time()
        reference_start_time = None
        reasoning_start_time = None
        content_start_time = None

        # 准备请求参数
        request_id = str(uuid.uuid4())
        conversation_id = str(uuid.uuid4())

        payload = {
            "query": query,
            "user_id": user_id,
            "model_id": model_id,
            "msg_id": request_id,
            "conversation_id": conversation_id,
            "history": self.rag_conversation_history[::-1],
            "stream": True,
            "top_k": top_k
        }

        # 调用API并实时更新
        reference_content = ""
        reasoning_content = ""
        content_content = ""
        reference_time = ""
        reasoning_time = ""
        content_time = ""

        try:
            url = f"{self.api_url}/rag-qa"
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                async with client.stream("POST", url, json=payload) as response:
                    response.raise_for_status()
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]
                            try:
                                chunk = json.loads(data)
                                chunk_type = chunk.get("type", "")
                                chunk_content = chunk.get("content", "")

                                if chunk_type == "reference":
                                    if reference_start_time is None:
                                        reference_start_time = time.time()
                                    reference_content += chunk_content
                                    formatted_reference = self.format_reference_display(reference_content)
                                    current_time = time.time() - reference_start_time
                                    reference_time = f"⏱️ {current_time:.2f}秒"
                                    yield history_display, formatted_reference, reasoning_content, content_content, reference_time, reasoning_time, content_time
                                elif chunk_type == "reasoning":
                                    if reasoning_start_time is None:
                                        reasoning_start_time = time.time()
                                        if reference_start_time:
                                            reference_time = f"⏱️ {reasoning_start_time - reference_start_time:.2f}秒"
                                    reasoning_content += chunk_content
                                    formatted_reference = self.format_reference_display(reference_content)
                                    current_time = time.time() - reasoning_start_time
                                    reasoning_time = f"⏱️ {current_time:.2f}秒"
                                    yield history_display, formatted_reference, reasoning_content, content_content, reference_time, reasoning_time, content_time
                                elif chunk_type == "content":
                                    if content_start_time is None:
                                        content_start_time = time.time()
                                        if reasoning_start_time:
                                            reasoning_time = f"⏱️ {content_start_time - reasoning_start_time:.2f}秒"
                                    content_content += chunk_content
                                    formatted_reference = self.format_reference_display(reference_content)
                                    current_time = time.time() - content_start_time
                                    content_time = f"⏱️ {current_time:.2f}秒"
                                    yield history_display, formatted_reference, reasoning_content, content_content, reference_time, reasoning_time, content_time
                            except json.JSONDecodeError:
                                continue
        except Exception as e:
            error_msg = f"API调用失败: {str(e)}"
            content_content = error_msg
            formatted_reference = self.format_reference_display(reference_content)
            yield history_display, formatted_reference, reasoning_content, content_content, reference_time, reasoning_time, content_time

        # 更新历史对话
        self.rag_conversation_history.append({"query": query, "content": content_content})

        # 更新历史显示
        timestamp = datetime.now().strftime("%H:%M:%S")
        final_time = time.time() - start_time
        new_history = f"{history_display}\n\n**[{timestamp}] 用户：**\n{query}\n\n**[{timestamp}] 助手：**\n{content_content}\n⏱️ 总耗时: {final_time:.2f}秒"

        formatted_reference = self.format_reference_display(reference_content)
        yield new_history, formatted_reference, reasoning_content, content_content, reference_time, reasoning_time, content_time

    async def chat_dataqa_stream(self, query: str, model_id: str, user_id: str, top_k: int, history_display: str):
        """DATAQA问答 - 流式输出版本"""
        if not query.strip():
            yield history_display, "", "", "", "", "", ""
            return

        # 记录开始时间
        start_time = time.time()
        reference_start_time = None
        reasoning_start_time = None
        content_start_time = None

        # 准备请求参数
        request_id = str(uuid.uuid4())
        conversation_id = str(uuid.uuid4())

        payload = {
            "query": query,
            "user_id": user_id,
            "model_id": model_id,
            "msg_id": request_id,
            "conversation_id": conversation_id,
            "history": self.dataqa_conversation_history[::-1],
            "stream": True,
            "top_k": top_k
        }

        # 调用API并实时更新
        reference_content = ""
        reasoning_content = ""
        content_content = ""
        reference_time = ""
        reasoning_time = ""
        content_time = ""

        try:
            url = f"{self.api_url}/data-qa"
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                async with client.stream("POST", url, json=payload) as response:
                    response.raise_for_status()
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]
                            try:
                                chunk = json.loads(data)
                                chunk_type = chunk.get("type", "")
                                chunk_content = chunk.get("content", "")

                                if chunk_type == "reference":
                                    if reference_start_time is None:
                                        reference_start_time = time.time()
                                    reference_content += chunk_content
                                    formatted_reference = self.format_reference_display(reference_content)
                                    current_time = time.time() - reference_start_time
                                    reference_time = f"⏱️ {current_time:.2f}秒"
                                    yield history_display, formatted_reference, reasoning_content, content_content, reference_time, reasoning_time, content_time
                                elif chunk_type == "reasoning":
                                    if reasoning_start_time is None:
                                        reasoning_start_time = time.time()
                                        if reference_start_time:
                                            reference_time = f"⏱️ {reasoning_start_time - reference_start_time:.2f}秒"
                                    reasoning_content += chunk_content
                                    formatted_reference = self.format_reference_display(reference_content)
                                    current_time = time.time() - reasoning_start_time
                                    reasoning_time = f"⏱️ {current_time:.2f}秒"
                                    yield history_display, formatted_reference, reasoning_content, content_content, reference_time, reasoning_time, content_time
                                elif chunk_type == "content":
                                    if content_start_time is None:
                                        content_start_time = time.time()
                                        if reasoning_start_time:
                                            reasoning_time = f"⏱️ {content_start_time - reasoning_start_time:.2f}秒"
                                    content_content += chunk_content
                                    formatted_reference = self.format_reference_display(reference_content)
                                    current_time = time.time() - content_start_time
                                    content_time = f"⏱️ {current_time:.2f}秒"
                                    yield history_display, formatted_reference, reasoning_content, content_content, reference_time, reasoning_time, content_time
                            except json.JSONDecodeError:
                                continue
        except Exception as e:
            error_msg = f"API调用失败: {str(e)}"
            content_content = error_msg
            formatted_reference = self.format_reference_display(reference_content)
            yield history_display, formatted_reference, reasoning_content, content_content, reference_time, reasoning_time, content_time

        # 更新历史对话
        self.dataqa_conversation_history.append({"query": query, "content": content_content})

        # 更新历史显示
        timestamp = datetime.now().strftime("%H:%M:%S")
        final_time = time.time() - start_time
        new_history = f"{history_display}\n\n**[{timestamp}] 用户：**\n{query}\n\n**[{timestamp}] 助手：**\n{content_content}\n⏱️ 总耗时: {final_time:.2f}秒"

        formatted_reference = self.format_reference_display(reference_content)
        yield new_history, formatted_reference, reasoning_content, content_content, reference_time, reasoning_time, content_time

def create_gradio_interface():
    """创建Gradio界面"""
    app = ChatApp()

    # 自定义CSS样式
    custom_css = """
    /* 全局容器样式 - 自适应浏览器窗口 */
    .gradio-container {
        font-size: 14px !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        height: 100vh !important;
        max-height: 100vh !important;
        margin: 0 !important;
        padding: 8px !important;
        box-sizing: border-box !important;
    }

    /* 主要布局区域 */
    .main-container {
        height: calc(100vh - 16px) !important;
        display: flex !important;
        flex-direction: row !important;
    }

    /* 左侧配置区域样式 */
    .config-panel {
        border: 1px solid #e0e0e0 !important;
        border-radius: 8px !important;
        padding: 12px !important;
        margin-right: 12px !important;
        background: #fafafa !important;
        min-width: 200px !important;
        max-width: 250px !important;
    }

    /* 右侧主要区域样式 */
    .main-panel {
        border: 1px solid #e0e0e0 !important;
        border-radius: 8px !important;
        padding: 12px !important;
        background: white !important;
        flex: 1 !important;
        overflow: hidden !important;
    }

    /* 组件边界样式 */
    .component-section {
        border: 1px solid #e8e8e8 !important;
        border-radius: 6px !important;
        padding: 8px !important;
        margin-bottom: 8px !important;
        background: #fcfcfc !important;
    }

    /* 标题样式 */
    .main-title {
        font-size: 28px !important;
        font-weight: bold !important;
        color: #2c3e50 !important;
        margin: 0 0 16px 0 !important;
        text-align: left !important;
    }

    .section-title {
        font-size: 16px !important;
        font-weight: 600 !important;
        color: #34495e !important;
        margin: 0 0 8px 0 !important;
        border-bottom: 2px solid #3498db !important;
        padding-bottom: 4px !important;
    }

    /* 按钮样式 */
    .gr-button {
        font-size: 13px !important;
        border-radius: 6px !important;
    }

    /* 输入框样式 */
    .gr-textbox {
        font-size: 13px !important;
        border-radius: 6px !important;
    }

    /* Markdown显示区域样式 */
    .gr-markdown {
        font-size: 14px !important;
        border: 1px solid #e8e8e8 !important;
        border-radius: 6px !important;
        padding: 12px !important;
        background: white !important;
    }

    /* 标签页样式 */
    .gr-tab-nav {
        font-size: 14px !important;
        border-bottom: 2px solid #e0e0e0 !important;
    }

    /* 表单间距 */
    .gr-form {
        gap: 6px !important;
    }

    /* 时间显示样式 */
    .time-display {
        font-size: 12px !important;
        color: #7f8c8d !important;
        background: #ecf0f1 !important;
        border: 1px solid #bdc3c7 !important;
        border-radius: 4px !important;
        padding: 4px 8px !important;
        text-align: center !important;
    }
    """

    # 包装异步生成器为同步生成器，支持流式输出
    def sync_chat_llm(query, model_id, user_id, history_display):
        async def async_gen():
            async for result in app.chat_llm_stream(query, model_id, user_id, history_display):
                yield result

        # 运行异步生成器
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            gen = async_gen()
            while True:
                try:
                    yield loop.run_until_complete(gen.__anext__())
                except StopAsyncIteration:
                    break
        finally:
            loop.close()

    def sync_chat_rag(query, model_id, user_id, top_k, history_display):
        async def async_gen():
            async for result in app.chat_rag_stream(query, model_id, user_id, top_k, history_display):
                yield result

        # 运行异步生成器
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            gen = async_gen()
            while True:
                try:
                    yield loop.run_until_complete(gen.__anext__())
                except StopAsyncIteration:
                    break
        finally:
            loop.close()

    def sync_chat_dataqa(query, model_id, user_id, top_k, history_display):
        async def async_gen():
            async for result in app.chat_dataqa_stream(query, model_id, user_id, top_k, history_display):
                yield result

        # 运行异步生成器
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            gen = async_gen()
            while True:
                try:
                    yield loop.run_until_complete(gen.__anext__())
                except StopAsyncIteration:
                    break
        finally:
            loop.close()

    with gr.Blocks(title=TITLE, theme=getattr(gr.themes, THEME.capitalize())(), css=custom_css) as interface:
        with gr.Row(elem_classes=["main-container"]):
            # 左侧配置区域
            with gr.Column(scale=1, min_width=200, elem_classes=["config-panel"]):
                # 大标题
                gr.Markdown(f"# {TITLE}", elem_classes=["main-title"])

                # 对话配置
                with gr.Group(elem_classes=["component-section"]):
                    gr.Markdown("**对话配置**", elem_classes=["section-title"])
                    user_id = gr.Textbox(
                        value="gradio_user",
                        label="用户ID",
                        container=True,
                        max_lines=1,
                        scale=1
                    )
                    conversation_id = gr.Textbox(
                        value="",
                        label="对话ID",
                        container=True,
                        max_lines=1,
                        placeholder="自动生成",
                        interactive=False,
                        scale=1
                    )

                # 模型配置
                with gr.Group(elem_classes=["component-section"]):
                    gr.Markdown("**模型配置**", elem_classes=["section-title"])
                    model_id = gr.Dropdown(
                        choices=DEFAULT_MODELS,
                        value=DEFAULT_MODEL,
                        label="模型",
                        container=True
                    )

                # 检索配置
                with gr.Group(elem_classes=["component-section"]):
                    gr.Markdown("**检索配置**", elem_classes=["section-title"])
                    top_k = gr.Slider(
                        minimum=1,
                        maximum=20,
                        value=DEFAULT_TOP_K,
                        step=1,
                        label="检索数量上限",
                        info="最多检索条数",
                        container=True
                    )

                # 对话历史清空
                with gr.Group(elem_classes=["component-section"]):
                    gr.Markdown("**对话历史清空**", elem_classes=["section-title"])
                    clear_btn = gr.Button("🗑️ 清空历史", variant="secondary", size="sm", scale=1)

            # 右侧主要区域
            with gr.Column(scale=4, elem_classes=["main-panel"]):
                # 使用标签页来区分三种API类型
                with gr.Tabs():
                    # LLM问答标签页
                    with gr.TabItem("🤖 Qwen模型", id="llm"):
                        # 输入区域
                        with gr.Row():
                            llm_query_input = gr.Textbox(
                                placeholder="一键解锁知识库内容，尽情合意问，体验AI Chat新技能",
                                lines=2,
                                show_label=False,
                                scale=8
                            )
                            llm_submit_btn = gr.Button("发送", variant="primary", size="sm", scale=1, min_width=60)

                        # 思考过程区域
                        with gr.Group(elem_classes=["component-section"]):
                            with gr.Row():
                                with gr.Column(scale=8):
                                    gr.Markdown("**🤔 思考过程**", elem_classes=["section-title"])
                                with gr.Column(scale=1):
                                    llm_reasoning_time = gr.Textbox(
                                        value="",
                                        lines=1,
                                        interactive=False,
                                        show_copy_button=False,
                                        container=False,
                                        elem_classes=["time-display"]
                                    )
                            llm_reasoning_display = gr.Markdown(
                                value="",
                                height=180,
                                container=False
                            )

                        # 回复内容区域
                        with gr.Group(elem_classes=["component-section"]):
                            with gr.Row():
                                with gr.Column(scale=8):
                                    gr.Markdown("**✅ 回复内容**", elem_classes=["section-title"])
                                with gr.Column(scale=1):
                                    llm_content_time = gr.Textbox(
                                        value="",
                                        lines=1,
                                        interactive=False,
                                        show_copy_button=False,
                                        container=False,
                                        elem_classes=["time-display"]
                                    )
                            llm_content_display = gr.Markdown(
                                value="",
                                height=220,
                                container=False
                            )

                        # 历史对话区域
                        with gr.Group(elem_classes=["component-section"]):
                            gr.Markdown("**📝 对话历史**", elem_classes=["section-title"])
                            llm_history_display = gr.Markdown(
                                value="",
                                height=250,
                                container=False
                            )

                    # RAG问答标签页
                    with gr.TabItem("📚 硬工知识库", id="rag"):
                        # 输入区域
                        with gr.Row():
                            rag_query_input = gr.Textbox(
                                placeholder="请输入您的问题，我将基于知识库为您解答",
                                lines=2,
                                show_label=False,
                                scale=8
                            )
                            rag_submit_btn = gr.Button("发送", variant="primary", size="sm", scale=1, min_width=60)

                        # 主要内容区域：左边参考知识，右边思考和回复
                        with gr.Row():
                            # 左侧：参考知识
                            with gr.Column(scale=1):
                                with gr.Group(elem_classes=["component-section"]):
                                    with gr.Row():
                                        with gr.Column(scale=8):
                                            gr.Markdown("**📚 知识库参考**", elem_classes=["section-title"])
                                        with gr.Column(scale=1):
                                            rag_reference_time = gr.Textbox(
                                                value="",
                                                lines=1,
                                                interactive=False,
                                                show_copy_button=False,
                                                container=False,
                                                elem_classes=["time-display"]
                                            )
                                    rag_reference_display = gr.Markdown(
                                        value="",
                                        height=400,
                                        container=False
                                    )

                            # 右侧：思考过程和回复内容
                            with gr.Column(scale=1):
                                # 思考过程
                                with gr.Group(elem_classes=["component-section"]):
                                    with gr.Row():
                                        with gr.Column(scale=8):
                                            gr.Markdown("**🤔 思考过程**", elem_classes=["section-title"])
                                        with gr.Column(scale=1):
                                            rag_reasoning_time = gr.Textbox(
                                                value="",
                                                lines=1,
                                                interactive=False,
                                                show_copy_button=False,
                                                container=False,
                                                elem_classes=["time-display"]
                                            )
                                    rag_reasoning_display = gr.Markdown(
                                        value="",
                                        height=180,
                                        container=False
                                    )

                                # 回复内容
                                with gr.Group(elem_classes=["component-section"]):
                                    with gr.Row():
                                        with gr.Column(scale=8):
                                            gr.Markdown("**✅ 回复内容**", elem_classes=["section-title"])
                                        with gr.Column(scale=1):
                                            rag_content_time = gr.Textbox(
                                                value="",
                                                lines=1,
                                                interactive=False,
                                                show_copy_button=False,
                                                container=False,
                                                elem_classes=["time-display"]
                                            )
                                    rag_content_display = gr.Markdown(
                                        value="",
                                        height=200,
                                        container=False
                                    )

                        # 历史对话
                        with gr.Group(elem_classes=["component-section"]):
                            gr.Markdown("**📝 对话历史**", elem_classes=["section-title"])
                            rag_history_display = gr.Markdown(
                                value="",
                                height=200,
                                container=False
                            )

                    # DATAQA问答标签页
                    with gr.TabItem("📊 R平台问答", id="dataqa"):
                        # 输入区域
                        with gr.Row():
                            dataqa_query_input = gr.Textbox(
                                placeholder="请输入数据相关问题，我将为您分析解答",
                                lines=2,
                                show_label=False,
                                scale=8
                            )
                            dataqa_submit_btn = gr.Button("发送", variant="primary", size="sm", scale=1, min_width=60)

                        # 主要内容区域：左边数据参考，右边思考和回复
                        with gr.Row():
                            # 左侧：数据参考
                            with gr.Column(scale=1):
                                with gr.Group(elem_classes=["component-section"]):
                                    with gr.Row():
                                        gr.Markdown("**📊 数据参考**", elem_classes=["section-title"], scale=8)
                                        dataqa_reference_time = gr.Textbox(
                                            value="",
                                            lines=1,
                                            interactive=False,
                                            show_copy_button=False,
                                            container=False,
                                            scale=1,
                                            elem_classes=["time-display"]
                                        )
                                    dataqa_reference_display = gr.Markdown(
                                        value="",
                                        height=400,
                                        container=False
                                    )

                            # 右侧：思考过程和回复内容
                            with gr.Column(scale=1):
                                # 思考过程
                                with gr.Group(elem_classes=["component-section"]):
                                    with gr.Row():
                                        gr.Markdown("**🤔 思考过程**", elem_classes=["section-title"], scale=8)
                                        dataqa_reasoning_time = gr.Textbox(
                                            value="",
                                            lines=1,
                                            interactive=False,
                                            show_copy_button=False,
                                            container=False,
                                            scale=1,
                                            elem_classes=["time-display"]
                                        )
                                    dataqa_reasoning_display = gr.Markdown(
                                        value="",
                                        height=180,
                                        container=False
                                    )

                                # 回复内容
                                with gr.Group(elem_classes=["component-section"]):
                                    with gr.Row():
                                        gr.Markdown("**✅ 回复内容**", elem_classes=["section-title"], scale=8)
                                        dataqa_content_time = gr.Textbox(
                                            value="",
                                            lines=1,
                                            interactive=False,
                                            show_copy_button=False,
                                            container=False,
                                            scale=1,
                                            elem_classes=["time-display"]
                                        )
                                    dataqa_content_display = gr.Markdown(
                                        value="",
                                        height=200,
                                        container=False
                                    )

                        # 历史对话
                        with gr.Group(elem_classes=["component-section"]):
                            gr.Markdown("**📝 对话历史**", elem_classes=["section-title"])
                            dataqa_history_display = gr.Markdown(
                                value="",
                                height=200,
                                container=False
                            )

        # LLM问答事件处理
        llm_submit_btn.click(
            fn=sync_chat_llm,
            inputs=[llm_query_input, model_id, user_id, llm_history_display],
            outputs=[llm_history_display, llm_reasoning_display, llm_content_display, llm_reasoning_time, llm_content_time]
        ).then(
            fn=lambda: "",  # 清空输入框
            outputs=[llm_query_input]
        )

        llm_query_input.submit(
            fn=sync_chat_llm,
            inputs=[llm_query_input, model_id, user_id, llm_history_display],
            outputs=[llm_history_display, llm_reasoning_display, llm_content_display, llm_reasoning_time, llm_content_time]
        ).then(
            fn=lambda: "",  # 清空输入框
            outputs=[llm_query_input]
        )

        # RAG问答事件处理
        rag_submit_btn.click(
            fn=sync_chat_rag,
            inputs=[rag_query_input, model_id, user_id, top_k, rag_history_display],
            outputs=[rag_history_display, rag_reference_display, rag_reasoning_display, rag_content_display, rag_reference_time, rag_reasoning_time, rag_content_time]
        ).then(
            fn=lambda: "",  # 清空输入框
            outputs=[rag_query_input]
        )

        rag_query_input.submit(
            fn=sync_chat_rag,
            inputs=[rag_query_input, model_id, user_id, top_k, rag_history_display],
            outputs=[rag_history_display, rag_reference_display, rag_reasoning_display, rag_content_display, rag_reference_time, rag_reasoning_time, rag_content_time]
        ).then(
            fn=lambda: "",  # 清空输入框
            outputs=[rag_query_input]
        )

        # DATAQA问答事件处理
        dataqa_submit_btn.click(
            fn=sync_chat_dataqa,
            inputs=[dataqa_query_input, model_id, user_id, top_k, dataqa_history_display],
            outputs=[dataqa_history_display, dataqa_reference_display, dataqa_reasoning_display, dataqa_content_display, dataqa_reference_time, dataqa_reasoning_time, dataqa_content_time]
        ).then(
            fn=lambda: "",  # 清空输入框
            outputs=[dataqa_query_input]
        )

        dataqa_query_input.submit(
            fn=sync_chat_dataqa,
            inputs=[dataqa_query_input, model_id, user_id, top_k, dataqa_history_display],
            outputs=[dataqa_history_display, dataqa_reference_display, dataqa_reasoning_display, dataqa_content_display, dataqa_reference_time, dataqa_reasoning_time, dataqa_content_time]
        ).then(
            fn=lambda: "",  # 清空输入框
            outputs=[dataqa_query_input]
        )

        # 清空历史事件 - 清空所有标签页的内容
        def clear_all_history():
            app.clear_history()
            return ("", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "")

        clear_btn.click(
            fn=clear_all_history,
            outputs=[
                llm_history_display, llm_reasoning_display, llm_content_display, llm_reasoning_time, llm_content_time, llm_query_input,
                rag_history_display, rag_reference_display, rag_reasoning_display, rag_content_display, rag_reference_time, rag_reasoning_time, rag_content_time, rag_query_input,
                dataqa_history_display, dataqa_reference_display, dataqa_reasoning_display, dataqa_content_display, dataqa_reference_time, dataqa_reasoning_time, dataqa_content_time, dataqa_query_input
            ]
        )

    return interface

if __name__ == "__main__":
    # 创建并启动界面
    interface = create_gradio_interface()
    interface.launch(
        server_name="127.0.0.1",
        server_port=7863,
        share=False,
        debug=False
    )
