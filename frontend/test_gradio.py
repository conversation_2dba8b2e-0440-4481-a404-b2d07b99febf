#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Gradio测试应用
"""

import gradio as gr

def simple_chat(message):
    return f"收到消息: {message}"

# 创建简单界面
with gr.Blocks() as demo:
    gr.Markdown("# 测试Gradio应用")
    
    with gr.<PERSON>():
        with gr.Column():
            input_text = gr.Textbox(label="输入")
            submit_btn = gr.<PERSON><PERSON>("提交")
        
        with gr.Column():
            output_text = gr.Textbox(label="输出")
    
    submit_btn.click(
        fn=simple_chat,
        inputs=[input_text],
        outputs=[output_text]
    )

if __name__ == "__main__":
    demo.launch(server_name="0.0.0.0", server_port=7860)
