import sys
import os
import unittest
import time
import uuid
import redis
import asyncio
from loguru import logger

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dotenv import load_dotenv

env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
else:
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)

from config.logging_config import configure_logging
from config.redis_config import get_redis_connection_params
from core.llm_provider import get_llm_provider
from services.chatflow_service import ConversationSession
from services.chat_storage_service import ConversationHistoryStorage

configure_logging()

class TestStreamingConversationSession(unittest.IsolatedAsyncioTestCase):
    """ConversationSession流式接口测试用例"""

    @classmethod
    def setUpClass(cls):
        redis_params = get_redis_connection_params()
        try:
            cls.redis_client = redis.StrictRedis(**redis_params)
            cls.redis_client.ping()
            logger.info("成功连接到Redis服务器")
            cls.llm_provider = get_llm_provider("gpt_4o")
            cls.conversation_session = ConversationSession(cls.llm_provider)
        except redis.exceptions.ConnectionError as e:
            logger.error(f"无法连接到Redis服务器: {e}")
            raise unittest.SkipTest("无法连接到Redis服务器")

    async def test_streaming_request(self):
        """测试流式对话接口"""
        system_prompt = "你是一个有用的AI助理，请简要回答用户问题。"
        user_query = "请用一句话介绍北京。"
        user_id = f"stream_user_{int(time.time())}"
        conversation_id = None

        logger.info("开始测试流式接口")
        chunks = []
        async for chunk in self.conversation_session.make_streaming_request(
            system_prompt, user_query, conversation_id, user_id
        ):
            self.assertIn("conversation_id", chunk)
            self.assertIn("text", chunk)
            self.assertIn("is_end", chunk)
            chunks.append(chunk)
            print(f"流式输出: {chunk['text']}", end="", flush=True)
            if chunk.get("is_end"):
                break

        # 校验最后一个chunk
        self.assertTrue(chunks[-1]["is_end"])
        self.assertIn("full_response", chunks[-1])
        self.assertTrue(len(chunks[-1]["full_response"]) > 0)
        logger.info("流式接口测试通过")

if __name__ == "__main__":
    unittest.main()