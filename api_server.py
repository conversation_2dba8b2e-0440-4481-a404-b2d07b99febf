from fastapi import FastAP<PERSON>, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import json
from direct_request_streaming import StreamChat

app = FastAPI(title="Stream Chat API")

# 配置信息
API_KEY = "sk-dms-Qwen3-32B-2025-05-23"
BASE_URL = "http://10.38.3.53:8000/v1"

# 初始化StreamChat
stream_chat = StreamChat(API_KEY, BASE_URL)

class ChatRequest(BaseModel):
    message: str

@app.post("/chat/stream")
async def chat_stream(request: ChatRequest):
    async def generate():  # 改为异步生成器
        async for response in stream_chat.chat_stream(request.message):  # 异步迭代
            yield f"data: {json.dumps(response, ensure_ascii=False)}\n\n"
    
    return StreamingResponse(
        generate(),  # 直接传递异步生成器
        media_type="text/event-stream"
    )

@app.get("/health")
async def health_check():
    return {"status": "healthy"}